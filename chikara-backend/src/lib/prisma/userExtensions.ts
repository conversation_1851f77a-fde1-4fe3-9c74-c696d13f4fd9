import gameConfig from "../../config/gameConfig.js";
import { LogErrorStack } from "../../utils/log.js";
import { GetNextAPRegenTime, GetNextHealingTick } from "../../utils/tickUtils.js";
import { Prisma, PrismaClient, user as UserType, SkillType } from "@prisma/client";
import { getSkillLevel } from "../../core/skills.service.js";
const { ENERGY_TICK_MS } = gameConfig;

const calculateCurrentEnergy = async (client: PrismaClient, user: UserType) => {
    try {
        if (!user) return false;
        // Allow 0 energy but guard null / undefined
        if (user.energy === null || user.energy === undefined) return false;
        const currentEnergy = user.energy || 0;
        const lastEnergyTick = user.lastEnergyTick ? Number(user.lastEnergyTick) : null;

        // If energy is full and has a lastEnergyTick, clear it
        if (currentEnergy === 100 && lastEnergyTick) {
            await client.user.update({
                where: { id: user.id },
                data: {
                    lastEnergyTick: null,
                },
            });
            return true;
        }

        // If energy isn't full and no lastEnergyTick, set it to start now
        if (currentEnergy < 100 && !lastEnergyTick) {
            LogErrorStack({
                error: new Error(
                    `User #${user.id} has no lastEnergyTick when energy is less than max. Energy: ${currentEnergy}. Last energy tick: ${lastEnergyTick}.`
                ),
            });

            await client.user.update({
                where: { id: user.id },
                data: {
                    lastEnergyTick: Date.now(),
                },
            });
            return true;
        }

        const currentTimestamp = Date.now();
        if (!lastEnergyTick) return false;

        const minutesSinceLastRegen = Math.floor((currentTimestamp - lastEnergyTick) / ENERGY_TICK_MS);

        const energyGained = Math.min(
            minutesSinceLastRegen, // Energy gained at 1 per minute
            100 - (user.energy || 0) // Cap at max energy
        );

        if (energyGained > 0) {
            const newEnergy = Math.min(currentEnergy + energyGained, 100);

            // Update the user's energy and lastEnergyTick
            await client.user.update({
                where: { id: user.id },
                data: {
                    energy: (user.energy || 0) + energyGained,
                    lastEnergyTick: newEnergy === 100 ? null : currentTimestamp,
                },
            });

            return true;
        }

        return false;
    } catch (error) {
        LogErrorStack({ error });
        return false;
    }
};

const handleExpiredTimestamps = async (client: PrismaClient, result: UserType[]) => {
    // eslint-disable-next-line unicorn/no-nested-ternary
    const users = Array.isArray(result) ? result : result ? [result] : [];
    let hasUpdates = false;

    for (const user of users) {
        if (!user.hospitalisedUntil && !user.banExpires && !user.profileDetailBanUntil && !user.chatBannedUntil) {
            continue;
        }
        const now = Date.now();

        if (user.hospitalisedUntil && user.hospitalisedUntil < now) {
            const updateData: {
                hospitalisedUntil: null;
                hospitalisedReason: null;
                hospitalisedHealingType: null;
                currentHealth?: number;
            } = {
                hospitalisedUntil: null,
                hospitalisedReason: null,
                hospitalisedHealingType: null,
            };

            if (user.hospitalisedHealingType === "full") {
                updateData.currentHealth = user.health || 0;
            }

            await client.user.update({
                where: { id: user.id },
                data: updateData,
            });
            await client.user_status_effect.deleteMany({
                where: {
                    userId: user.id,
                },
            });
        }

        if (user.banExpires && user.banExpires < new Date()) {
            await client.user.update({
                where: { id: user.id },
                data: { banExpires: null },
            });
        }

        if (user.profileDetailBanUntil && user.profileDetailBanUntil < now) {
            await client.user.update({
                where: { id: user.id },
                data: { profileDetailBanUntil: null },
            });
        }

        if (user.chatBannedUntil && user.chatBannedUntil < now) {
            await client.user.update({
                where: { id: user.id },
                data: { chatBannedUntil: null },
            });
        }

        hasUpdates = true;
    }

    if (hasUpdates) {
        return true;
    }

    return false;
};

export default Prisma.defineExtension((client) => {
    return client.$extends({
        result: {
            user: {
                // Computed field for max health including vitality bonuses
                maxHealth: {
                    needs: { id: true, health: true },
                    compute: async (user) => {
                        try {
                            // Get vitality level from skill system
                            const vitality = await getSkillLevel(user.id, SkillType.vitality);

                            // Base health from user model
                            const baseHealth = user.health;

                            // Apply vitality bonuses
                            // Flat bonus: +20 HP per vitality point
                            const flatBonus = vitality * 20;

                            // Percentage bonus: +0.65% per vitality point
                            const percentageMultiplier = 1 + vitality * 0.0065;

                            // Calculate final max health (round down to nearest 10)
                            const calculatedMaxHealth =
                                Math.floor(((baseHealth + flatBonus) * percentageMultiplier) / 10) * 10;

                            return calculatedMaxHealth;
                        } catch (error) {
                            LogErrorStack({ message: `Failed to calculate max health for user ${user.id}`, error });
                            // Fallback to base health if calculation fails
                            return user.health;
                        }
                    },
                },
            },
        },
        query: {
            user: {
                async $allOperations({ operation, args, query }) {
                    const result = await query(args);
                    let valueUpdated = false;

                    if (operation.startsWith("find")) {
                        if (operation === "findUnique") {
                            valueUpdated = await calculateCurrentEnergy(client as PrismaClient, result as UserType);
                        }

                        const timestampUpdated = await handleExpiredTimestamps(
                            client as PrismaClient,
                            result as UserType[]
                        );
                        valueUpdated = valueUpdated || timestampUpdated;
                    }

                    if (valueUpdated) {
                        return await query(args);
                    }

                    return result;
                },
                async update({ args, query }) {
                    if (args.data.currentHealth) {
                        args.data.nextHPTick = GetNextHealingTick();
                    }
                    if (args.data.actionPoints) {
                        args.data.nextAPTick = GetNextAPRegenTime();
                    }

                    return await query(args);
                },
            },
        },
    });
});
