import gameConfig from "../../config/gameConfig.js";
import * as InventoryService from "../../core/inventory.service.js";
import * as NotificationService from "../../core/notification.service.js";
import * as StatusEffectService from "../../core/statuseffect.service.js";
import * as UserService from "../../core/user.service.js";
import { potentialDailyChestRewards } from "../../data/dailyChestRewards.js";
import * as BattleHelpers from "../battle/helpers/battle.helpers.js";
import * as ItemRepository from "../../repositories/item.repository.js";
import { logAction } from "../../lib/actionLogger.js";
import { NotificationTypes } from "../../types/notification.js";
import { logger } from "../../utils/log.js";
import { Prisma } from "@prisma/client";
import * as UserRepository from "../../repositories/user.repository.js";

const {
    HOSPITALISE_ITEM_NAME,
    REVIVE_ITEM_NAME,
    JAIL_ITEM_NAME,
    DAILY_CHEST_ITEM_ID,
    SM_RAW_MATERIALS_ITEM_NAME,
    SM_TOOLS_ITEM_NAME,
    MEGAPHONE_ITEM_NAME,
    KOMPROMAT_JAIL_TIME_MS,
} = gameConfig;

let cachedMegaphoneItemId: number | null = null;

const cacheMegaphoneItemId = async () => {
    const item = await ItemRepository.findItemByName(MEGAPHONE_ITEM_NAME);
    if (item) {
        cachedMegaphoneItemId = item.id;
    }
    return cachedMegaphoneItemId;
};

export const useDeathNote = async (userId: number, targetId: number, injuryName: string, injuryType: string) => {
    const deathNoteItem = await ItemRepository.findItemByName(HOSPITALISE_ITEM_NAME);
    if (!deathNoteItem) {
        return { error: "Item not found", statusCode: 404 };
    }

    if (!(await InventoryService.UserHasItem(userId, deathNoteItem.id))) {
        return { error: "You don't own the item", statusCode: 400 };
    }

    if (!targetId || !injuryName || !injuryType) {
        return { error: "Missing required fields", statusCode: 400 };
    }

    const target = await UserRepository.getUserById(targetId);

    if (!target) {
        return { error: "User not found", statusCode: 400 };
    }

    if ((await BattleHelpers.IsUserInBattle(target)) || target.hospitalisedUntil) {
        return { error: "User cannot currently be targeted", statusCode: 400 };
    }

    if (target.userType === "admin") {
        return { error: "Immune", statusCode: 400 };
    }

    await InventoryService.SubtractItemFromUser({ userId, itemId: deathNoteItem.id, amount: 1 });

    NotificationService.NotifyUser(target.id, NotificationTypes.death_noted, { injuryName });

    logAction({
        action: "USED_ITEM_DEATHNOTE",
        userId: userId,
        info: {
            targetId: target.id,
            targetUsername: target.username,
            injuryName,
            injuryType,
        },
    });

    const effect = await StatusEffectService.GetInjuryOfType(injuryType, "Severe");

    if (!effect) {
        return { error: "No suitable injury found", statusCode: 500 };
    }

    await StatusEffectService.ApplyStatusEffectToUser(target, effect, injuryName);
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { roguelikeMap, maxHealth, defeatedNpcs, ...safeTarget } = target;
    await ItemRepository.updateUserHealth(target.id, {
        ...safeTarget,
        roguelikeMap: roguelikeMap ?? Prisma.DbNull,
        defeatedNpcs: defeatedNpcs ?? Prisma.DbNull,
    });

    return { data: "Success" };
};

export const useLifeNote = async (userId: number, targetId: number) => {
    const lifeNoteItem = await ItemRepository.findItemByName(REVIVE_ITEM_NAME);
    if (!lifeNoteItem) {
        return { error: "Item not found", statusCode: 404 };
    }

    if (!(await InventoryService.UserHasItem(userId, lifeNoteItem.id))) {
        return { error: "You don't own the item", statusCode: 400 };
    }

    if (!targetId) {
        return { error: "Invalid request", statusCode: 400 };
    }

    const target = await UserRepository.findUserHealthInfo(targetId);

    if (!target) {
        return { error: "User not found", statusCode: 400 };
    }

    if ((await BattleHelpers.IsUserInBattle(target)) || target.hospitalisedUntil) {
        return { error: "User cannot currently be healed", statusCode: 400 };
    }

    await InventoryService.SubtractItemFromUser({ userId, itemId: lifeNoteItem.id, amount: 1 });

    NotificationService.NotifyUser(targetId, NotificationTypes.life_noted, { initiatorId: userId });

    logAction({
        action: "USED_ITEM_LIFENOTE",
        userId: userId,
        info: {
            targetId: target.id,
            targetUsername: target.username,
        },
    });

    await ItemRepository.updateUserHealth(target.id, {
        hospitalisedUntil: null,
        hospitalisedHealingType: null,
        hospitalisedReason: null,
        currentHealth: target.health,
    });

    await StatusEffectService.removeUserStatusEffects(target.id);

    return { data: "Success" };
};

export const useKompromat = async (userId: number, targetId: number, reason: string) => {
    const jailItem = await ItemRepository.findItemByName(JAIL_ITEM_NAME);
    if (!jailItem) {
        return { error: "Item not found", statusCode: 404 };
    }

    if (!(await InventoryService.UserHasItem(userId, jailItem.id))) {
        return { error: "You don't own the item", statusCode: 400 };
    }

    if (!targetId || !reason) {
        return { error: "Invalid request", statusCode: 400 };
    }

    const target = await UserRepository.findUserHealthInfo(targetId);

    if (!target) {
        return { error: "User not found", statusCode: 404 };
    }

    if (target.hospitalisedUntil || target.jailedUntil || (await BattleHelpers.IsUserInBattle(target))) {
        return { error: "User cannot currently be jailed", statusCode: 400 };
    }

    if (target.userType === "admin") {
        return { error: "Immune", statusCode: 400 };
    }

    await InventoryService.SubtractItemFromUser({ userId, itemId: jailItem.id, amount: 1 });

    logger.info(`User ${userId} used a kompromat on user ${target.id} (${target.username}) with reason: ${reason})`);

    logAction({
        action: "USED_ITEM_KOMPROMAT",
        userId: userId,
        info: {
            targetId: target.id,
            targetUsername: target.username,
            reason,
        },
    });

    await UserService.JailUser(target.id, KOMPROMAT_JAIL_TIME_MS, reason, {
        notificationType: NotificationTypes.kompromat_jailed,
    });

    return { data: "Success" };
};

interface RewardItem {
    itemId: number;
    itemQuantity: number;
    itemName: string;
    dropRate: number;
}

const getRandomRewardId = () => {
    const totalDropRate = potentialDailyChestRewards.reduce((acc: number, drop: RewardItem) => acc + drop.dropRate, 0);
    const random = Math.random() * totalDropRate;

    let cumulativeRate = 0;
    for (const reward of potentialDailyChestRewards) {
        cumulativeRate += reward.dropRate;
        if (random <= cumulativeRate) {
            return { itemId: reward.itemId, itemQuantity: reward.itemQuantity, itemName: reward.itemName };
        }
    }

    return { itemId: 1, itemQuantity: 20, itemName: "Pen" };
};

export const getDailyChestItems = () => {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const rewardsWithoutDropRate = potentialDailyChestRewards.map(({ dropRate, ...rest }) => rest);
    return { data: rewardsWithoutDropRate };
};

export const simulateChest = () => {
    const items: { itemId: number; itemQuantity: number; itemName: string }[] = [];
    const itemQuantities: Record<string, number> = {};

    for (let i = 0; i < 1000; i++) {
        const itemReward = getRandomRewardId();
        items.push(itemReward);

        itemQuantities[itemReward.itemName] = (itemQuantities[itemReward.itemName] || 0) + 1;
    }

    const aggregatedItems = Object.keys(itemQuantities).map((itemName) => ({
        itemName: itemName,
        itemQuantity: itemQuantities[itemName],
    }));

    return { data: aggregatedItems };
};

export const useDailyChest = async (userId: number) => {
    if (!(await InventoryService.UserHasItem(userId, DAILY_CHEST_ITEM_ID))) {
        return { error: "You don't own the item", statusCode: 400 };
    }

    const itemReward = getRandomRewardId();
    if (!itemReward) {
        return { error: "Can't get reward!", statusCode: 400 };
    }

    await InventoryService.SubtractItemFromUser({ userId, itemId: DAILY_CHEST_ITEM_ID, amount: 1 });
    await InventoryService.AddItemToUser({
        userId,
        itemId: itemReward.itemId,
        amount: itemReward.itemQuantity,
        isTradeable: true,
    });

    logAction({
        action: "DAILYCHEST_REWARD",
        userId: userId,
        info: {
            itemId: itemReward.itemId,
            itemName: itemReward.itemName,
            itemQuantity: itemReward.itemQuantity,
        },
    });

    return { data: itemReward };
};

export const useMegaPhone = async (userId: number, message: string) => {
    const megaphoneItemId = cachedMegaphoneItemId || (await cacheMegaphoneItemId());
    if (!megaphoneItemId) {
        return { error: "Item not found", statusCode: 404 };
    }

    const hasItem = await InventoryService.UserHasItem(userId, megaphoneItemId);
    if (!hasItem) {
        return { error: "You don't own the item", statusCode: 400 };
    }

    const users = await ItemRepository.findAllUserIds();
    const userIds = users.map((user) => user.id);

    await InventoryService.SubtractItemFromUser({ userId, itemId: megaphoneItemId, amount: 1 });
    await ItemRepository.createGlobalMessages(userId, message, userIds);

    return { data: "Success" };
};

export const useRawMaterialsCrate = async (userId: number) => {
    const rawMaterialAmount = 10;

    const crateItem = await ItemRepository.findItemByName(SM_RAW_MATERIALS_ITEM_NAME);
    if (!crateItem) {
        return { error: "Item not found", statusCode: 404 };
    }

    const user = await UserRepository.findUserWithGangInfo(userId);
    if (!user || !user.gangId) {
        return { error: "You need to be in a gang to use this!", statusCode: 400 };
    }

    const gang = await ItemRepository.findGangById(user.gangId);
    if (!gang) {
        return { error: "Can't find gang", statusCode: 404 };
    }

    const gangMember = await ItemRepository.findGangMember(userId);
    if (!gangMember) {
        return { error: "Can't find gang member", statusCode: 404 };
    }

    await ItemRepository.updateGangResources(gang.id, rawMaterialAmount);
    await ItemRepository.updateGangMemberResources(gangMember.id, rawMaterialAmount);
    await InventoryService.SubtractItemFromUser({ userId, itemId: crateItem.id, amount: 1 });

    logAction({
        action: "REDEEM_SMALL_MATERIALS_CRATE",
        userId: userId,
        info: {},
    });

    return { data: "Success" };
};

export const useToolsCrate = async (userId: number) => {
    const toolsAmount = 10;

    const crateItem = await ItemRepository.findItemByName(SM_TOOLS_ITEM_NAME);
    if (!crateItem) {
        return { error: "Item not found", statusCode: 404 };
    }

    const user = await UserRepository.findUserWithGangInfo(userId);
    if (!user || !user.gangId) {
        return { error: "You need to be in a gang to use this!", statusCode: 400 };
    }

    const gang = await ItemRepository.findGangById(user.gangId);
    if (!gang) {
        return { error: "Can't find gang", statusCode: 404 };
    }

    const gangMember = await ItemRepository.findGangMember(userId);
    if (!gangMember) {
        return { error: "Can't find gang member", statusCode: 404 };
    }

    await ItemRepository.updateGangResources(gang.id, 0, toolsAmount);
    await ItemRepository.updateGangMemberResources(gangMember.id, 0, toolsAmount);
    await InventoryService.SubtractItemFromUser({ userId, itemId: crateItem.id, amount: 1 });

    logAction({
        action: "REDEEM_SMALL_TOOLS_CRATE",
        userId: userId,
        info: {},
    });

    return { data: "Success" };
};
