import gameConfig from "../../config/gameConfig.js";
import * as GangRepository from "../../repositories/gang.repository.js";
import { logAction } from "../../lib/actionLogger.js";
import { GangMemberModel, GangModel, UserModel } from "../../lib/db.js";
import { LogErrorStack } from "../../utils/log.js";

const { DAILY_ESSENCE_CAP, BASE_LIFE_ESSENCE_REWARD, ELO_K_FACTOR } = gameConfig;

type GangMemberWithContribution = GangMemberModel & {
    weeklyContribution?: number;
    payoutPercentage?: number;
};

export async function logGangAction(
    gangId: number,
    action: string,
    info: string,
    gangMemberId: number | null = null,
    secondPartyId: number | null = null
): Promise<void> {
    try {
        await GangRepository.createGangLog({
            gang: { connect: { id: gangId } },
            action,
            info,
            user_gang_log_gangMemberIdTouser: gangMemberId ? { connect: { id: gangMemberId } } : undefined,
            user_gang_log_secondPartyIdTouser: secondPartyId ? { connect: { id: secondPartyId } } : undefined,
        });
    } catch (error) {
        LogErrorStack({ message: "Failed to log gang action:", error });
    }
}

const essencePenaltyModifier = (currentUser: UserModel, target: UserModel): number => {
    // Capped at + 100% or -90%
    const MAX_MODIFIER = 1.5; // +100%
    const MIN_MODIFIER = 0.35; // -90%

    // Calculate the modifier based on the difference in combat levels
    let modifier = 1 - (currentUser.combatLevel - target.combatLevel) * 0.0004;

    // Ensure the modifier is within the defined caps
    modifier = Math.max(Math.min(modifier, MAX_MODIFIER), MIN_MODIFIER);

    return modifier;
};

const getLifeEssenceReward = (user: UserModel, target: UserModel): number => {
    const baseEssence = BASE_LIFE_ESSENCE_REWARD || 5;
    const essenceModifier = essencePenaltyModifier(user, target);
    const essenceTotal = Math.round(Math.max(1, baseEssence * essenceModifier));

    return essenceTotal;
};

const respectPenaltyModifier = (currentUser: UserModel, target: UserModel): number => {
    const MAX_MODIFIER = 1.25;
    const MIN_MODIFIER = 0.25;

    const levelDifference = target.combatLevel - currentUser.combatLevel;
    let modifier = 1 + Math.log(Math.abs(levelDifference) + 1) * 0.1;

    if (levelDifference < 0) {
        modifier = 1 - Math.log(Math.abs(levelDifference) + 1) * 0.125;
    }

    modifier = Math.max(Math.min(modifier, MAX_MODIFIER), MIN_MODIFIER);

    return modifier;
};

function calculateEloExpectation(scoreA: number, scoreB: number): number {
    const transformA = Math.pow(10, scoreA / 400);
    const transformB = Math.pow(10, scoreB / 400);
    const expectedA = transformA / (transformA + transformB);
    return expectedA;
}

export async function AddGangLifeEssence(
    currentUser: UserModel,
    target: UserModel,
    amount: number,
    action: string
): Promise<void> {
    const gang = await GangRepository.findGangWithEssence(currentUser.gangId!);
    const gangMember = await GangRepository.findGangMemberWithEssence(currentUser.id);

    if (!gang || !gangMember) {
        return;
    }

    if ((gang.dailyEssenceGained ?? 0) >= DAILY_ESSENCE_CAP) {
        return;
    }

    let essenceReward = amount;

    if ((gang.dailyEssenceGained ?? 0) + amount > DAILY_ESSENCE_CAP) {
        essenceReward = DAILY_ESSENCE_CAP - (gang.dailyEssenceGained ?? 0);
    }

    await GangRepository.updateGangEssenceAndRespect(gang, {
        essenceResource: (gang.essenceResource ?? 0) + essenceReward,
        dailyEssenceGained: (gang.dailyEssenceGained ?? 0) + essenceReward,
    });

    await GangRepository.updateGangMemberEssenceAndRespect(gangMember, {
        weeklyEssence: (gangMember.weeklyEssence ?? 0) + essenceReward,
    });

    logGangAction(gang.id, action, String(essenceReward), currentUser.id, target.id);
}

interface GangRewards {
    essenceReward: number | null;
    respectReward: number | null;
    targetRespectChange: number | null;
}

export async function CollectGangRewards(user: UserModel, target: UserModel): Promise<GangRewards> {
    let essenceReward: number | null = null;
    let respectReward: number | null = null;
    let targetRespectChange: number | null = null;

    const gang = await GangRepository.findGangWithRespectAndResources(user.gangId!);
    const targetGang = target.gangId ? await GangRepository.findGangWithRespectAndResources(target.gangId) : null;

    if (!gang) {
        return { essenceReward, respectReward, targetRespectChange };
    }

    const gangMember = await GangRepository.findGangMemberWithEssenceAndRespect(user.id);

    if (!gangMember) {
        return { essenceReward, respectReward, targetRespectChange };
    }

    if (!targetGang && (gang.dailyEssenceGained ?? 0) >= DAILY_ESSENCE_CAP) {
        return { essenceReward, respectReward, targetRespectChange };
    }

    essenceReward = getLifeEssenceReward(user, target);
    if ((gang.dailyEssenceGained ?? 0) + essenceReward > DAILY_ESSENCE_CAP) {
        essenceReward = DAILY_ESSENCE_CAP - (gang.dailyEssenceGained ?? 0);
    }

    if (targetGang) {
        const currentGangRespectScore = gang.weeklyRespect ?? 0;
        const targetGangRespectScore = targetGang.weeklyRespect ?? 0;

        const expectedScore = calculateEloExpectation(currentGangRespectScore, targetGangRespectScore);
        const respectEloScore = Math.round(ELO_K_FACTOR * (1 - expectedScore));
        const targetRespectEloScore = Math.round(ELO_K_FACTOR * (0 - (1 - expectedScore)));
        const penaltyModifier = respectPenaltyModifier(user, target);

        respectReward = Math.max(Math.round(respectEloScore * penaltyModifier), 0);
        targetRespectChange = Math.max(Math.round(targetRespectEloScore * penaltyModifier), 0);

        await GangRepository.updateGangEssenceAndRespect(targetGang, {
            weeklyRespect: (targetGang.weeklyRespect ?? 0) + targetRespectChange,
        });

        if (targetRespectChange !== null) {
            logGangAction(targetGang.id, "RespectLost", String(targetRespectChange), target.id, user.id);
        }

        await GangRepository.updateGangEssenceAndRespect(gang, {
            weeklyRespect: (gang.weeklyRespect ?? 0) + respectReward,
            totalRespect: (gang.totalRespect ?? 0) + respectReward,
            essenceResource: (gang.essenceResource ?? 0) + essenceReward,
            dailyEssenceGained: (gang.dailyEssenceGained ?? 0) + essenceReward,
        });

        await GangRepository.updateGangMemberEssenceAndRespect(gangMember, {
            weeklyRespect: (gangMember.weeklyRespect ?? 0) + respectReward,
            weeklyEssence: (gangMember.weeklyEssence ?? 0) + essenceReward,
        });

        if (respectReward !== null) {
            logGangAction(gang.id, "RespectGained", String(respectReward), user.id, target.id);
        }
    } else {
        await GangRepository.updateGangEssenceAndRespect(gang, {
            essenceResource: (gang.essenceResource ?? 0) + essenceReward,
            dailyEssenceGained: (gang.dailyEssenceGained ?? 0) + essenceReward,
        });

        await GangRepository.updateGangMemberEssenceAndRespect(gangMember, {
            weeklyEssence: (gangMember.weeklyEssence ?? 0) + essenceReward,
        });
    }

    logAction({
        action: "GANG_LIFE_ESSENCE_GAINED",
        userId: user.id,
        info: {
            targetId: target.id,
            targetUsername: target.username,
            essenceGained: essenceReward,
        },
    });

    if (respectReward) {
        logAction({
            action: "GANG_RESPECT_GAINED",
            userId: user.id,
            info: {
                targetId: target.id,
                targetUsername: target.username,
                respectGained: respectReward,
                targetRespectChange: targetRespectChange,
            },
        });
    }

    return { essenceReward, respectReward, targetRespectChange };
}

const rankMultipliers: Record<number, number> = {
    1: 1, // Rookie
    2: 1.1, // Member
    3: 1.2, // Thug
    4: 1.3, // Officer
    5: 1.4, // Lieutenant
    6: 1.5, // Leader
};

const gangRankingMultipliers: Record<number, number> = {
    1: 1.5,
    2: 1.3,
    3: 1.15,
};

const getWeeklyContributionTotal = (member: GangMemberModel): number => {
    return (member.weeklyMaterials ?? 0) + (member.weeklyEssence ?? 0) * 3 + (member.weeklyTools ?? 0) * 2;
};

const getMemberTax = (earnedCreds: number, memberAmount: number): number => {
    const taxPerPlayer = 0.02;
    const totalTax = memberAmount * taxPerPlayer;
    return Math.round(earnedCreds * totalTax);
};

export function GetGangRankingMultiplier(gangsList: GangModel[], currentGang: GangModel): number {
    const sortedGangs = gangsList?.sort((a, b) => (b.weeklyRespect ?? 0) - (a.weeklyRespect ?? 0));
    const currentRank = sortedGangs.findIndex((g) => g.id === currentGang.id);
    return gangRankingMultipliers[currentRank + 1] || 1;
}

export function CalculateGangWeeklyEarnings(members: GangMemberWithContribution[], rankingMultiplier: number): number {
    const contributionTotal = members.reduce((acc, member) => {
        const weeklyContribution = getWeeklyContributionTotal(member);
        member.weeklyContribution = weeklyContribution;
        return acc + weeklyContribution;
    }, 0);
    const earnedCreds = contributionTotal / 5;

    const totalTax = getMemberTax(earnedCreds, members.length);
    const totalEarnedCreds = earnedCreds * rankingMultiplier;
    return Math.max(totalEarnedCreds - totalTax, 0);
}

export function CalculateGangPayoutShares(members: GangMemberWithContribution[], totalPool: number) {
    const memberShares = members.map((member) => {
        const individualContribution = member.weeklyContribution!;
        const baseShare = totalPool > 0 ? individualContribution / totalPool : 0;
        const rankMultiplier = rankMultipliers[member.rank] || 1;
        const adjustedShare = baseShare * rankMultiplier;

        return {
            ...member,
            payoutShare: adjustedShare,
        };
    });

    const totalShare = memberShares.reduce((sum, member) => sum + member.payoutShare!, 0);

    return memberShares.map((member) => ({
        ...member,
        payoutPercentage: totalShare > 0 ? (member.payoutShare! / totalShare) * 100 : 0,
    }));
}
