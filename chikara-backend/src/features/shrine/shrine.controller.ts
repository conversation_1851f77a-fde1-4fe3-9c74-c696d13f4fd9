import gameConfig from "../../config/gameConfig.js";
import * as UserService from "../../core/user.service.js";
import * as ShrineHelper from "./shrine.helpers.js";
import { logAction } from "../../lib/actionLogger.js";
import { getToday } from "../../utils/dateHelpers.js";
import { emitShrineDonationMade } from "../../core/events/index.js";
import * as UserRepository from "../../repositories/user.repository.js";
import * as ShrineRepository from "../../repositories/shrine.repository.js";

const { SHRINE_MINIMUM_DONATION } = gameConfig;

export const getDailyShrineGoal = async (date: Date) => {
    return await ShrineHelper.getDailyShrineGoal(date);
};

export const isTodaysDonationGoalReached = async () => {
    const today = getToday();
    return await ShrineRepository.findDailyShrineGoalWithReached(today);
};

export const getDailyShrineDonations = async (date: Date) => {
    return await ShrineHelper.dailyShrineDonations(date);
};

export const donateToShrine = async (userId: number, amount: number) => {
    if (!amount) {
        return { error: "Amount is required and must be a number", statusCode: 400 };
    }
    if (amount < 0 || amount < SHRINE_MINIMUM_DONATION) {
        return { error: "Invalid amount", statusCode: 400 };
    }

    const currentUser = await UserRepository.getUserById(userId);
    if (!currentUser) {
        return { error: "User not found", statusCode: 404 };
    }
    if (currentUser.cash < amount) {
        return { error: "Not enough cash", statusCode: 400 };
    }

    const dailyShrineGoal = await ShrineHelper.getDailyShrineGoal(getToday());
    if (!dailyShrineGoal) {
        return { error: "No daily shrine goal set for today", statusCode: 400 };
    }

    const updatedCash = (currentUser.cash -= amount);
    await UserService.updateUser(currentUser.id, { cash: updatedCash });

    const updatedShrineGoal = await ShrineHelper.addToDailyShrineGoal(currentUser, amount);
    if (!updatedShrineGoal) {
        return { error: "Failed to update shrine goal", statusCode: 500 };
    }

    await emitShrineDonationMade({
        userId: currentUser.id,
        amount,
    });

    logAction({
        action: "SHRINE_DONATION",
        userId: currentUser.id,
        info: {
            amount,
            totalDonations: updatedShrineGoal.donationAmount,
        },
    });

    return { data: `Donated ${amount} to daily shrine goal` };
};
