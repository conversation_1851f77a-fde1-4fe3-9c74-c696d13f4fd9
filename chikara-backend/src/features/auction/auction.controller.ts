import gameConfig from "../../config/gameConfig.js";
import AchievementHelpers from "../../core/achievement.service.js";
import * as InventoryService from "../../core/inventory.service.js";
import * as NotificationService from "../../core/notification.service.js";
import * as AuctionRepository from "../../repositories/auction.repository.js";
import { logAction } from "../../lib/actionLogger.js";
import { db } from "../../lib/db.js";
import { NotificationTypes } from "../../types/notification.js";
import { getNow } from "../../utils/dateHelpers.js";
import { LogErrorStack } from "../../utils/log.js";
import type { AuctionItemStatus, ItemTypes } from "@prisma/client";
import * as UserRepository from "../../repositories/user.repository.js";
import * as ItemRepository from "../../repositories/item.repository.js";

const { ALLOWED_AUCTION_ITEM_TYPES, BLACKLISTED_AUCTION_ITEM_IDS } = gameConfig;

export const getAuctionList = async () => {
    const date = getNow();
    const auctionItems = await AuctionRepository.findAuctionItems(date);
    return { data: auctionItems };
};

export function calculateAuctionEndsAt(auctionLength: number) {
    const currentTime = getNow();
    currentTime.setHours(currentTime.getHours() + auctionLength);
    return currentTime;
}

const AUCTION_FEES: number[] = [0.03, 0.06, 0.09];
const VALID_AUCTION_LENGTHS = {
    12: AUCTION_FEES[0],
    24: AUCTION_FEES[1],
    48: AUCTION_FEES[2],
};

export const createAuctionListing = async (
    userId: number,
    itemId: number,
    quantity: number,
    buyoutPrice: number,
    auctionLength: number,
    bankFunds: boolean
) => {
    // Validate the input data
    if (!itemId || !userId || !quantity || !buyoutPrice || !auctionLength) {
        return { error: "All fields are required." };
    }

    const auctionFee = VALID_AUCTION_LENGTHS[auctionLength as keyof typeof VALID_AUCTION_LENGTHS];

    if (!auctionFee) {
        return { error: "Invalid auction length. Must be 12, 24, or 48 hours." };
    }

    // Verify that the item and user exist
    const item = await ItemRepository.findItemById(itemId);
    const currentUser = await UserRepository.getUserById(userId);

    if (!item) {
        return { error: "Item not found." };
    }

    if (!currentUser) {
        return { error: "User not found." };
    }

    if (!(ALLOWED_AUCTION_ITEM_TYPES as readonly ItemTypes[]).includes(item.itemType)) {
        return { error: "Can't list this item type!" };
    }

    if ((BLACKLISTED_AUCTION_ITEM_IDS as readonly number[]).includes(item.id)) {
        return { error: "This item is blacklisted!" };
    }

    if (!(await InventoryService.UserHasTradeableNumberOfItem(userId, itemId, quantity))) {
        return { error: "User does not have enough of item ID: " + itemId };
    }

    const totalFee = Math.max(buyoutPrice * quantity * auctionFee, 50);

    if (currentUser.cash < totalFee) {
        return { error: "Insufficient funds to list item" };
    }

    const endsAt = calculateAuctionEndsAt(auctionLength);

    await UserRepository.decrementUserCash(userId, totalFee);
    await InventoryService.SubtractItemFromUser({
        userId,
        itemId,
        amount: quantity,
        isTradeable: true,
        upgradeLevel: 0,
    });

    const auctionItem = await AuctionRepository.createAuctionItem({
        itemId,
        sellerId: userId,
        quantity,
        deposit: 0,
        buyoutPrice,
        endsAt,
        bankFunds,
    });

    logAction({
        action: "AUCTION_LISTING_CREATED",
        userId: userId,
        info: {
            auctionId: auctionItem.id,
            itemId,
            itemName: item.name,
            quantity,
            buyoutPrice,
        },
    });

    return { data: auctionItem };
};

export const buyoutAuctionListing = async (buyerId: number, auctionItemId: number, quantity: number) => {
    if (!auctionItemId || !buyerId) {
        return { error: "Auction item ID and buyer ID are required." };
    }
    if (!quantity) {
        return { error: "Quantity is required." };
    }

    const auctionItem = await AuctionRepository.findAuctionItemWithDetails(auctionItemId);

    if (!auctionItem) {
        return { error: "Auction item not found or not active." };
    }

    if (!auctionItem.sellerId || !auctionItem.itemId || !auctionItem.item) {
        return { error: "Auction item invalid." };
    }

    if (auctionItem.sellerId === buyerId) {
        return { error: "Can't buy your own auction item." };
    }

    if (quantity > auctionItem.quantity) {
        return { error: "Quantity cannot exceed the number of items in the auction." };
    }

    const buyer = await UserRepository.getUserById(buyerId);

    if (!buyer) {
        return { error: "Buyer not found." };
    }

    const totalCost = auctionItem.buyoutPrice * quantity;

    if (buyer.cash < totalCost) {
        return { error: "Insufficient funds." };
    }

    try {
        const result = await db.$transaction(async (prismaTx) => {
            const seller = await AuctionRepository.findSellerTransaction(prismaTx, auctionItem.sellerId!);
            if (!seller) throw new Error("Seller not found");

            const newQuantity = auctionItem.quantity > quantity ? auctionItem.quantity - quantity : 0;
            const status: AuctionItemStatus = auctionItem.quantity > quantity ? "in_progress" : "complete";

            let sellerBankBalance;
            if (auctionItem.bankFunds) {
                sellerBankBalance = seller.bank_balance + auctionItem.buyoutPrice * 0.85;
            }

            const updatedAuctionItem = await AuctionRepository.executeAuctionTransaction(
                prismaTx,
                buyerId,
                buyer.cash - totalCost,
                seller.id,
                seller.cash + totalCost,
                auctionItemId,
                newQuantity,
                status,
                auctionItem.bankFunds,
                sellerBankBalance
            );

            // Add item to buyer's inventory
            await InventoryService.AddItemToUser({
                userId: buyer.id,
                itemId: auctionItem.itemId!,
                amount: quantity,
                isTradeable: false,
                tx: prismaTx,
            });

            return { seller, updatedAuctionItem };
        });

        const { seller, updatedAuctionItem } = result;

        await AchievementHelpers.UpdateUserAchievement(seller.id, "marketItemsSold", quantity);
        await AchievementHelpers.UpdateUserAchievement(seller.id, "marketMoneyMade", totalCost);

        NotificationService.NotifyUser(seller.id, NotificationTypes.auction_item_sold, {
            itemName: auctionItem.item.name,
            quantity,
            totalAmount: totalCost,
            buyerId: buyer.id,
            listingComplete: updatedAuctionItem.status === "complete",
            fundsBanked: auctionItem.bankFunds,
        });

        logAction({
            action: "AUCTION_ITEM_PURCHASED",
            userId: buyerId,
            info: {
                auctionId: auctionItem.id,
                itemId: auctionItem.item.id,
                itemName: auctionItem.item.name,
                quantity,
                totalCost,
            },
        });

        return { data: updatedAuctionItem };
    } catch (error) {
        LogErrorStack({ message: "Transaction error", error });
        return { statusCode: 500, error: "Unexpected error buying auction item." };
    }
};

export const cancelAuctionListing = async (sellerId: number, auctionItemId: number) => {
    if (!auctionItemId || !sellerId) {
        return { error: "Auction item ID and seller ID are required." };
    }

    const auctionItem = await AuctionRepository.findAuctionItemWithDetails(auctionItemId);

    if (!auctionItem) {
        return { error: "Auction item not found or not active." };
    }

    if (!auctionItem.sellerId || !auctionItem.itemId || !auctionItem.item) {
        return { error: "Auction item invalid." };
    }

    if (auctionItem.sellerId !== sellerId) {
        return { error: "Can't cancel auction item that is not your own." };
    }

    try {
        await db.$transaction(async (prismaTx) => {
            await AuctionRepository.updateAuctionItemStatus(auctionItemId, "cancelled");
            await InventoryService.AddItemToUser({
                userId: sellerId,
                itemId: auctionItem.itemId!,
                amount: auctionItem.quantity,
                isTradeable: true,
                tx: prismaTx,
            });
        });

        logAction({
            action: "AUCTION_LISTING_CANCELLED",
            userId: sellerId,
            info: {
                auctionId: auctionItem.id,
                itemId: auctionItem.itemId!,
                itemName: auctionItem.item!.name,
                quantity: auctionItem.quantity,
            },
        });
        return { data: "Auction item successfully cancelled." };
    } catch (error) {
        LogErrorStack({ message: "Unexpected transaction error cancelling auction item.", error });
        return { statusCode: 500, error: "Unexpected error cancelling auction item." };
    }
};
