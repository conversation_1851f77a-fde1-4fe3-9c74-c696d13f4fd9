import gameConfig from "../../config/gameConfig.js";
import * as NotificationService from "../../core/notification.service.js";
import * as BountyRepository from "../../repositories/bounty.repository.js";
import { logAction } from "../../lib/actionLogger.js";
import { BountyModel, UserModel } from "../../lib/db.js";
import { NotificationTypes } from "../../types/notification.js";
import { emitBountyCollected } from "../../core/events/index.js";
import * as UserRepository from "../../repositories/user.repository.js";

const { DEFAULT_STAFF_BOUNTY_USER_ID } = gameConfig;

interface CollectBountyParams {
    bounty: BountyModel;
    currentUser: UserModel;
    target: UserModel;
}

export const ApplyBounty = async (
    targetId: number,
    amount: number,
    reason: string,
    placerId: number = DEFAULT_STAFF_BOUNTY_USER_ID
) => {
    const existingBounty = await BountyRepository.findActiveBountyByTargetId(targetId);

    let bounty: BountyModel;
    if (existingBounty) {
        bounty = await BountyRepository.updateBountyAmount(existingBounty.id, existingBounty.amount + amount);
    } else {
        bounty = await BountyRepository.createBounty({
            amount,
            reason,
            placerId,
            targetId,
        });
    }

    NotificationService.NotifyUser(targetId, NotificationTypes.bounty_placed, {
        placerId,
        reason,
        bountyAmount: amount,
    });

    return bounty;
};

export const CollectBounty = async ({ bounty, currentUser, target }: CollectBountyParams) => {
    await BountyRepository.updateBountyClaimStatus(bounty.id, currentUser.id, false);

    const updatedUser = await UserRepository.incrementUserCash(currentUser.id, bounty.amount);
    if (!updatedUser) {
        throw new Error("Failed to update user cash");
    }

    await emitBountyCollected({
        userId: currentUser.id,
        bountyId: bounty.id,
        amount: bounty.amount,
    });

    NotificationService.NotifyUser(currentUser.id, NotificationTypes.bounty_claimed, {
        bountyId: bounty.id,
        bountyAmount: bounty.amount,
        targetId: bounty.targetId,
        placerId: bounty.placerId,
    });
    if (bounty.placerId) {
        NotificationService.NotifyUser(bounty.placerId, NotificationTypes.bounty_claimed, {
            bountyId: bounty.id,
            bountyAmount: bounty.amount,
            targetId: bounty.targetId,
            placerId: bounty.placerId,
            claimedById: currentUser.id,
        });
    }
    logAction({
        action: "BOUNTY_CLAIMED",
        userId: currentUser.id,
        info: {
            bountyId: bounty.id,
            amount: bounty.amount,
            targetId: bounty.targetId,
            targetUsername: target.username,
            placerId: bounty.placerId,
        },
    });
};

export default {
    ApplyBounty,
    CollectBounty,
};
