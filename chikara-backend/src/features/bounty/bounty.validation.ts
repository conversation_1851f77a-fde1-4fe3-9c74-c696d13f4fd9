import gameConfig from "../../config/gameConfig.js";
import z from "zod";

const { MIN_BOUNTY } = gameConfig;

export const placeBountySchema = z.object({
    targetId: z.number().int().positive(),
    amount: z.number().int().positive().min(MIN_BOUNTY),
    reason: z.string().min(1).max(255),
});

export const deleteBountySchema = z.object({
    id: z.number().int().positive(),
});

export default {
    placeBountySchema,
    deleteBountySchema,
};
