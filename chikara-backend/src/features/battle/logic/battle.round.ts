import { BATTLE_STATE } from "../helpers/battle.constants.js";
import * as BattleHelpers from "../helpers/battle.helpers.js";
import { ProcessAction, ActionResult } from "../helpers/battle.action.js";
import { addCombatLogEntry, logActionResult } from "../helpers/battle.logging.js";
import { LogErrorStack, logger } from "../../../utils/log.js";
import type { BattlePlayer, BattleState } from "../types/battle.types.js";
import * as BattleResolver from "../logic/battle.resolver.js";
import BattleAI from "../ai/battle.ai.js";
import * as TalentHelper from "../../talents/talents.helpers.js";
import gameConfig from "../../../config/gameConfig.js";
import { ItemModel } from "../../../lib/db.js";
import { getStaminaRegenModifier } from "../helpers/battle.scaling.js";
import * as UserRepository from "../../../repositories/user.repository.js";

const { BASE_STAMINA_REGEN } = gameConfig;

/**
 * Executes a single actor's turn in battle, handling AI decision-making, damage calculation, and effects.
 */
const executeActorTurn = async (
    actor: BattlePlayer,
    target: BattlePlayer,
    attackerState: BattlePlayer,
    targetState: BattlePlayer,
    playerAction?: string
) => {
    // Decide the action (human-chosen or AI-derived)
    const isHuman = actor.id === attackerState.id && attackerState.userType === "player";
    logger.debug(`Choosing action for ${actor.username} (${actor.userType}), isHuman: ${isHuman}`);

    const chosenAction =
        isHuman && playerAction
            ? playerAction
            : await (async () => {
                  const aiAction = await BattleAI.chooseAction(actor, target);
                  logger.debug(`AI chose action ${aiAction} for ${actor.userType} ${actor.username}`);
                  return aiAction;
              })();

    logger.debug(`Processing action ${chosenAction} for ${actor.username}`);
    // Execute and normalise the result
    let rawResult: number | ActionResult;
    try {
        rawResult = await ProcessAction(actor, target, chosenAction);
        logger.debug(`Action ${chosenAction} completed for ${actor.username}`);
    } catch (error) {
        // If action fails (e.g., not enough ammo, stamina, etc.), return 0 damage
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger.debug(`Action ${chosenAction} failed for ${actor.userType} ${actor.username}: ${errorMessage}`);
        rawResult = 0;
    }
    const damage = Math.round(typeof rawResult === "number" ? rawResult : rawResult.damage);
    const fleeAttempt = typeof rawResult === "number" ? undefined : rawResult.metadata?.fleeAttempt;

    // Normalize actionType for flee attempts to include success/fail
    let normalizedActionType = chosenAction;
    if (chosenAction === "flee" && fleeAttempt !== undefined) {
        normalizedActionType = fleeAttempt.success ? "flee_success" : "flee_failed";
    }

    // Effects are applied to the authoritative state (attackerState/targetState)
    const stateRef = actor.id === attackerState.id ? attackerState : targetState;
    const lifeSteal = BattleHelpers.ApplyLifeSteal(stateRef, chosenAction, damage);
    const bleedAmount = BattleHelpers.ApplyBleed(stateRef);

    return { actionType: normalizedActionType, damage, lifeSteal, bleedAmount, fleeAttempt };
};

/**
 * Processes stamina regeneration for a single player.
 */
const processPlayerStaminaRegeneration = async (player: BattlePlayer) => {
    logger.debug(`Processing stamina regeneration for player: ${player.username} (${player.userType})`);

    // Only check for talents if the player is a real player (not NPC)
    const recoveryTalent = player.userType === "player" ? await TalentHelper.UserHasRecoveryTalent(player.id) : null;

    // Base stamina regeneration with intelligence scaling
    let baseStaminaRegen: number = BASE_STAMINA_REGEN;

    // Apply intelligence-based scaling for players (NPCs don't have intelligence stats)
    if (player.userType === "player") {
        const intelligenceModifier = getStaminaRegenModifier(player.attributes.intelligence);
        baseStaminaRegen = Math.round(BASE_STAMINA_REGEN * intelligenceModifier);
    }

    // Apply recovery talent modifier if present
    let staminaRegenAmount = recoveryTalent ? baseStaminaRegen * (recoveryTalent.modifier || 1) : baseStaminaRegen;

    // Check for stamina regeneration debuff
    if (player.statusEffects["stamina_regen_debuff"]) {
        staminaRegenAmount = 0;
    }

    // Apply stamina regeneration
    player.currentStamina = Math.min(player.currentStamina + staminaRegenAmount, player.maxStamina);
};

// TODO - Fix for new system
const applyCombatRegeneration = async (
    attackerState: BattlePlayer,
    targetState: BattlePlayer,
    victory: boolean,
    enemyVictory: boolean
) => {
    logger.debug(`Applying combat regeneration - victory: ${victory}, enemyVictory: ${enemyVictory}`);
    const battleOver = victory || enemyVictory;
    const attackerCombatRegen =
        attackerState.userType === "player"
            ? await TalentHelper.UserHasCombatRegenerationTalent(attackerState.id)
            : null;
    const targetCombatRegen =
        targetState.userType === "player" ? await TalentHelper.UserHasCombatRegenerationTalent(targetState.id) : null;

    if (!attackerCombatRegen && !targetCombatRegen) {
        return;
    }

    if (battleOver) {
        if (victory && attackerCombatRegen) {
            BattleHelpers.ApplyCombatRegen(attackerState, attackerCombatRegen.modifier);
        }
        if (enemyVictory && targetCombatRegen) {
            BattleHelpers.ApplyCombatRegen(targetState, targetCombatRegen.modifier);
        }
    } else {
        if (attackerCombatRegen) {
            BattleHelpers.ApplyCombatRegen(attackerState, attackerCombatRegen.modifier);
        }
        if (targetCombatRegen) {
            BattleHelpers.ApplyCombatRegen(targetState, targetCombatRegen.modifier);
        }
    }
};

export const processBattleRound = async (
    battleState: BattleState,
    attackerState: BattlePlayer,
    targetState: BattlePlayer,
    action: string
) => {
    logger.debug(`processBattleRound started for battle: ${battleState.id}`);

    // Determine turn order
    const firstActor = battleState.firstAttackerId === attackerState.id ? attackerState : targetState;
    const secondActor = battleState.firstAttackerId === attackerState.id ? targetState : attackerState;

    logger.debug(`Executing first actor turn: ${firstActor.username} (${firstActor.userType})`);
    // Execute first actor's turn
    const firstTurnResult = await executeActorTurn(firstActor, secondActor, attackerState, targetState, action);

    // Handle first actor's flee attempt
    if (firstTurnResult.fleeAttempt && firstTurnResult.fleeAttempt.success) {
        // Only attempt DB lookup if the fleeing actor is a real player (numeric id)
        if (firstActor.userType === "player") {
            const currentUser = await UserRepository.getUserById(Number(firstActor.id));
            if (currentUser) {
                await BattleResolver.handleSuccessfulFlee(
                    battleState,
                    firstActor.id,
                    firstActor,
                    secondActor,
                    currentUser
                );
            } else {
                battleState.state = BATTLE_STATE.FINISHED;
            }
        } else {
            // NPC fleeing – no user record
            battleState.state = BATTLE_STATE.FINISHED;
        }

        const fleeingAction = { damage: 0, type: "flee_success", lifeSteal: 0, bleedAmount: 0 } as const;
        const emptyAction = { damage: 0, type: "", lifeSteal: 0, bleedAmount: 0 } as const;

        // Log the flee action result before returning
        logActionResult(battleState, firstActor, secondActor, {
            actionType: firstTurnResult.actionType,
            damage: firstTurnResult.damage,
            lifeSteal: firstTurnResult.lifeSteal,
            bleedAmount: firstTurnResult.bleedAmount,
        });

        // Decide which response field should contain the flee action
        const isPlayerFlee = firstActor.id === attackerState.id;

        return {
            battleState,
            attackedFirst: battleState.firstAttackerId,
            playerAction: isPlayerFlee ? fleeingAction : emptyAction,
            enemyAction: isPlayerFlee ? emptyAction : fleeingAction,
            flee: "success",
        };
    }

    // Apply damage to second actor (only if no successful flee)
    if (!firstTurnResult.fleeAttempt?.success) {
        secondActor.currentHealth = Math.max(0, secondActor.currentHealth - firstTurnResult.damage);
    }

    // Check if second actor is defeated
    const secondActorDefeated = secondActor.currentHealth <= 0;
    const battleResult = { victory: false, enemyVictory: false };

    let secondTurnResult = {
        actionType: "",
        damage: 0,
        lifeSteal: 0,
        bleedAmount: 0,
        fleeAttempt: undefined as { success: boolean } | undefined,
    };

    if (secondActorDefeated) {
        // Battle is won by first actor
        battleResult.victory = true;
    } else {
        logger.debug(`Executing second actor turn: ${secondActor.username} (${secondActor.userType})`);
        // Execute second actor's turn
        secondTurnResult = await executeActorTurn(secondActor, firstActor, attackerState, targetState, action);

        // Handle second actor's flee attempt
        if (secondTurnResult.fleeAttempt && secondTurnResult.fleeAttempt.success) {
            // Only attempt DB lookup if the fleeing actor is a real player (numeric id)
            if (secondActor.userType === "player") {
                const currentUser = await UserRepository.getUserById(Number(secondActor.id));
                if (currentUser) {
                    await BattleResolver.handleSuccessfulFlee(
                        battleState,
                        secondActor.id,
                        secondActor,
                        firstActor,
                        currentUser
                    );
                } else {
                    battleState.state = BATTLE_STATE.FINISHED;
                }
            } else {
                // NPC fleeing – no user record
                battleState.state = BATTLE_STATE.FINISHED;
            }

            const fleeingAction = { damage: 0, type: "flee_success", lifeSteal: 0, bleedAmount: 0 } as const;
            const firstActorAction = {
                damage: firstTurnResult.damage,
                type: firstTurnResult.actionType,
                lifeSteal: firstTurnResult.lifeSteal,
                bleedAmount: firstTurnResult.bleedAmount,
            } as const;

            // Log both the first actor's action and the successful flee before returning
            logActionResult(battleState, firstActor, secondActor, {
                actionType: firstTurnResult.actionType,
                damage: firstTurnResult.damage,
                lifeSteal: firstTurnResult.lifeSteal,
                bleedAmount: firstTurnResult.bleedAmount,
            });
            logActionResult(battleState, secondActor, firstActor, {
                actionType: secondTurnResult.actionType,
                damage: secondTurnResult.damage,
                lifeSteal: secondTurnResult.lifeSteal,
                bleedAmount: secondTurnResult.bleedAmount,
            });

            const playerFled = secondActor.id === attackerState.id;

            return {
                battleState,
                attackedFirst: battleState.firstAttackerId,
                playerAction: playerFled ? fleeingAction : firstActorAction,
                enemyAction: playerFled ? firstActorAction : fleeingAction,
                flee: "success",
            };
        }

        // Apply damage to first actor (only if no successful flee)
        if (!secondTurnResult.fleeAttempt?.success) {
            firstActor.currentHealth = Math.max(0, firstActor.currentHealth - secondTurnResult.damage);
        }

        // Check battle conclusion
        const firstActorDefeated = firstActor.currentHealth <= 0;
        if (firstActorDefeated) {
            // Determine victory based on which actor is the attacker
            if (secondActor.id === attackerState.id) {
                battleResult.victory = true;
            } else {
                battleResult.enemyVictory = true;
            }
        } else {
            // Check final battle state
            battleResult.victory = targetState.currentHealth <= 0;
            battleResult.enemyVictory = attackerState.currentHealth <= 0;
        }
    }

    logger.debug(`Processing stamina regeneration for battle: ${battleState.id}`);
    // Handle stamina regeneration for both players
    await processPlayerStaminaRegeneration(attackerState);
    await processPlayerStaminaRegeneration(targetState);

    logger.debug(`Updating status effects for battle: ${battleState.id}`);
    // Update status effects (for both actors - this happens *after* attacks)
    BattleHelpers.SubtractTurnFromStatusEffects(attackerState);
    BattleHelpers.SubtractTurnFromStatusEffects(targetState);

    // Track damage taken for quests if NPC battle
    if (targetState.userType !== "player") {
        attackerState.damageTaken +=
            battleState.firstAttackerId === attackerState.id ? secondTurnResult.damage : firstTurnResult.damage;
    }

    //Battle Result handling
    const rewardInfo: { droppedItem?: ItemModel; droppedCrate?: ItemModel; xpReward?: number } = {};

    if (battleResult.enemyVictory) {
        logger.debug(`Enemy victory - handling player defeat for ${attackerState.username}`);
        const currentUser = await UserRepository.getUserById(Number.parseInt(attackerState.id));

        if (!currentUser) {
            LogErrorStack({
                message: `User not found for attackerState: ${attackerState.id}`,
                error: new Error(`User not found for attackerState: ${attackerState.id}`),
            });
            return { error: "User not found" };
        }

        // Use centralized player defeat handler
        await BattleResolver.handlePlayerDefeat(
            battleState,
            attackerState,
            targetState,
            currentUser,
            "normal_loss",
            false // Don't cleanup here, battle continues for post-battle processing
        );
        logger.debug(`Player defeat handled for ${attackerState.username}`);
    }

    if (battleResult.victory) {
        logger.debug(`Player victory - handling NPC defeat for ${targetState.username}`);
        battleState.state = BATTLE_STATE.FINISHED;
        // TODO - Handle victory rewards
        if (
            battleState.battleType === "pve" ||
            battleState.battleType === "pve-rooftop" ||
            battleState.battleType === "pve-explore"
        ) {
            await BattleResolver.handleVictoryNPC(battleState, attackerState, targetState, rewardInfo);
        }
        logger.debug(`NPC defeat handled for ${targetState.username}`);
    }

    // --- Combat Log (conditional) ---

    // Always log the first actor's action
    logActionResult(battleState, firstActor, secondActor, firstTurnResult);

    // Log the second actor's action only if they were not defeated
    if (!secondActorDefeated) {
        logActionResult(battleState, secondActor, firstActor, secondTurnResult);
    }

    // Handle combat regeneration talents (after combat log, before round increment)
    await applyCombatRegeneration(attackerState, targetState, battleResult.victory, battleResult.enemyVictory);

    battleState.currentRound++;

    if (battleResult.victory) {
        addCombatLogEntry("battle_win", battleState, attackerState.id, targetState.id);
    }

    if (battleResult.enemyVictory) {
        addCombatLogEntry("battle_win", battleState, targetState.id, attackerState.id);
    }

    // Check if there was a failed flee attempt to include in response
    const playerFailedFlee =
        (firstTurnResult.fleeAttempt && !firstTurnResult.fleeAttempt.success) ||
        (secondTurnResult.fleeAttempt && !secondTurnResult.fleeAttempt.success);

    logger.debug(`processBattleRound completed for battle: ${battleState.id}`);
    return {
        battleState,
        attackedFirst: battleState.firstAttackerId,
        playerAction: {
            damage: firstActor.id === attackerState.id ? firstTurnResult.damage : secondTurnResult.damage,
            type: firstActor.id === attackerState.id ? firstTurnResult.actionType : secondTurnResult.actionType,
            userLifeSteal: firstActor.id === attackerState.id ? firstTurnResult.lifeSteal : secondTurnResult.lifeSteal,
            userBleedAmount:
                firstActor.id === attackerState.id ? firstTurnResult.bleedAmount : secondTurnResult.bleedAmount,
        },
        enemyAction: {
            damage: firstActor.id === targetState.id ? firstTurnResult.damage : secondTurnResult.damage,
            type: firstActor.id === targetState.id ? firstTurnResult.actionType : secondTurnResult.actionType,
            targetLifeSteal: firstActor.id === targetState.id ? firstTurnResult.lifeSteal : secondTurnResult.lifeSteal,
            targetBleedAmount:
                firstActor.id === targetState.id ? firstTurnResult.bleedAmount : secondTurnResult.bleedAmount,
        },
        ...(playerFailedFlee && { flee: "failed" }),
        ...rewardInfo,
    };
};
