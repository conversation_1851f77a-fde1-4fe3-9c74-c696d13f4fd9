import gameConfig from "../../../../config/gameConfig.js";
import * as NotificationService from "../../../../core/notification.service.js";
import * as ShrineHelper from "../../../../features/shrine/shrine.helpers.js";
import { db } from "../../../../lib/db.js";
import { LogErrorStack } from "../../../../utils/log.js";

const { AP_NOTIFICATIONS_ENABLED } = gameConfig;

export const processAPIncreases = async (): Promise<void> => {
    // logger.profile("processAPIncreases");
    try {
        const shrineBuffActive = await ShrineHelper.dailyBuffIsActive("apRegen");
        const users = await db.user.findMany({
            where: {
                actionPoints: {
                    lt: db.user.fields.maxActionPoints,
                },
            },
        });

        let apIncrease = 1;
        if (shrineBuffActive && Math.random() <= 0.4) {
            apIncrease = 2;
        }

        const pushNotificationsToSend: { userId: number; token: string }[] = [];

        for (const user of users) {
            let userAPIncrease = apIncrease;
            if (user.actionPoints + apIncrease > user.maxActionPoints) {
                userAPIncrease = 1;
            }

            try {
                const updatedUser = await db.user.update({
                    where: { id: user.id },
                    data: { actionPoints: { increment: userAPIncrease } },
                });

                if (updatedUser.actionPoints >= updatedUser.maxActionPoints && updatedUser.pushNotificationsEnabled) {
                    const pushTokens = await NotificationService.fetchUserPushTokens(user.id);
                    if (pushTokens && pushTokens.length > 0) {
                        for (const token of pushTokens) {
                            if (token.userId) {
                                pushNotificationsToSend.push({
                                    userId: token.userId,
                                    token: token.token,
                                });
                            }
                        }
                    }
                }
            } catch (error: unknown) {
                LogErrorStack({ error });
            }
        }

        if (pushNotificationsToSend.length > 0 && AP_NOTIFICATIONS_ENABLED) {
            const message = `Your action points have fully regenerated!`;
            await NotificationService.sendPushNotifications(pushNotificationsToSend, message);
        }
    } catch (error: unknown) {
        LogErrorStack({ message: "Failed to process AP increases:", error });
    }
    // logger.profile("processAPIncreases");
};
