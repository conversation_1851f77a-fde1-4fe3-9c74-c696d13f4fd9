import gameConfig from "../../../../config/gameConfig.js";
import * as NotificationService from "../../../../core/notification.service.js";
import * as BattleHelpers from "../../../../features/battle/helpers/battle.helpers.js";
import * as TalentHelper from "../../../../features/talents/talents.helpers.js";
import { db } from "../../../../lib/db.js";
import { LogErrorStack } from "../../../../utils/log.js";
import { GetNextHealingTick } from "../../../../utils/tickUtils.js";

const { HEALTH_REGEN_AMOUNT } = gameConfig;
const { HEALTH_NOTIFICATIONS_ENABLED } = gameConfig;

export const processHealthIncreases = async (): Promise<void> => {
    // logger.profile("processHealthIncreases");
    try {
        const users = await db.user.findMany({
            where: {
                currentHealth: {
                    lt: db.user.fields.health,
                },
            },
        });

        const pushNotificationsToSend: { userId: number; token: string }[] = [];

        for (const user of users) {
            const currentHealth = user.currentHealth;
            const maxHealth = await user.maxHealth;

            if (currentHealth >= maxHealth) {
                // hp changed between query and now
                continue;
            }

            if (await BattleHelpers.IsUserInBattle(user)) {
                continue;
            }

            let incrementValue = maxHealth * HEALTH_REGEN_AMOUNT;
            const outOfCombatRegenTalent = await TalentHelper.UserHasOutOfCombatRegenerationTalent(user.id);
            if (outOfCombatRegenTalent) {
                incrementValue *= outOfCombatRegenTalent.modifier ?? 1;
            }

            if (currentHealth + incrementValue > maxHealth) {
                incrementValue = maxHealth - currentHealth;
            }

            await db.user.update({
                where: { id: user.id },
                data: {
                    currentHealth: {
                        increment: incrementValue,
                    },
                    nextHPTick: GetNextHealingTick(),
                },
            });

            if (user.pushNotificationsEnabled && currentHealth + incrementValue >= maxHealth) {
                const pushTokens = await NotificationService.fetchUserPushTokens(user.id);
                if (pushTokens && pushTokens.length > 0) {
                    const validTokens = pushTokens.filter(
                        (t): t is { userId: number; token: string } => t.userId !== null
                    );
                    pushNotificationsToSend.push(...validTokens);
                }
            }
        }

        if (pushNotificationsToSend.length > 0 && HEALTH_NOTIFICATIONS_ENABLED) {
            const message = `Your HP has fully regenerated!`;
            await NotificationService.sendPushNotifications(pushNotificationsToSend, message);
        }
    } catch (error) {
        LogErrorStack({ message: "Failed to process healing:", error });
    }
    // logger.profile("processHealthIncreases");
};
