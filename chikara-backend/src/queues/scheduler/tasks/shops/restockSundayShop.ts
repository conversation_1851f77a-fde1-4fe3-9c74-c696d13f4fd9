import gameConfig from "../../../../config/gameConfig.js";
import rareShopListings from "../../../../data/rareShopStock.js";
import * as ChatHelper from "../../../../features/chat/chat.helpers.js";
import { db } from "../../../../lib/db.js";
import { LogErrorStack, logger } from "../../../../utils/log.js";

const { LIMITED_STOCK_WEEKLY_PERSONAL_LIMIT } = gameConfig;

async function processRestock(): Promise<void> {
    logger.profile("processRestock");
    try {
        // Delete existing shop listings for shop id 6
        await db.shop_listing.deleteMany({
            where: { shopId: 6 },
        });

        // Create new shop listings
        await db.shop_listing.createMany({
            data: [...rareShopListings.sundayShopListings],
            skipDuplicates: true,
        });
    } catch (error) {
        LogErrorStack({ message: "Failed to restock Sunday shop:", error });
    }
    logger.profile("processRestock");
}

async function resetPersonalLimits(): Promise<void> {
    try {
        await db.user.updateMany({
            where: {
                weeklyBuyLimitRemaining: {
                    lt: LIMITED_STOCK_WEEKLY_PERSONAL_LIMIT,
                },
            },
            data: {
                weeklyBuyLimitRemaining: LIMITED_STOCK_WEEKLY_PERSONAL_LIMIT,
            },
        });
    } catch (error) {
        LogErrorStack({ message: "Failed to reset personal limits:", error });
    }
}

async function announceShopOpenSoon(): Promise<void> {
    try {
        await ChatHelper.SendAnnouncementMessage("shopOpenSoon", "The Sunday shop will open in 30 minutes!");
    } catch (error) {
        LogErrorStack({ message: "Failed to announce shop opening soon:", error });
    }
}

export async function restockSundayShop(): Promise<void> {
    try {
        await processRestock();
        await resetPersonalLimits();
        await announceShopOpenSoon();
    } catch (error) {
        LogErrorStack({ message: "Failed to process Sunday shop restock:", error });
    }
}

export default restockSundayShop;
